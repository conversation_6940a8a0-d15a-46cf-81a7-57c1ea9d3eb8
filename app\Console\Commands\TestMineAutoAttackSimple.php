<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Mob;
use App\Models\MineMark;
use App\Models\MineLocation;
use App\Services\MineDetectionService;
use App\Services\PlayerHealthService;
use App\Jobs\MineAutoAttackJob;
use Illuminate\Support\Facades\DB;

class TestMineAutoAttackSimple extends Command
{
    protected $signature = 'mine:test-auto-attack-simple {player_name=admin}';
    protected $description = 'Простой тест автоатак мобов в рудниках';

    public function handle()
    {
        $playerName = $this->argument('player_name');
        
        $this->info("=== ПРОСТОЙ ТЕСТ АВТОАТАК МОБОВ В РУДНИКАХ ===");
        $this->info("Игрок: {$playerName}");
        $this->newLine();

        // 1. Находим игрока
        $player = User::where('name', $playerName)->first();
        if (!$player) {
            $this->error("❌ Игрок '{$playerName}' не найден!");
            return;
        }

        // 2. Проверяем HP
        $playerHealthService = app(PlayerHealthService::class);
        $currentHP = $playerHealthService->getCurrentHP($player);
        $maxHP = $player->getMaxHP();
        
        $this->info("👤 Игрок: {$player->name} (ID: {$player->id})");
        $this->info("💚 HP: {$currentHP}/{$maxHP}");
        
        // 3. Находим локацию рудника с мобами
        $mineLocationData = DB::table('mine_locations')
            ->join('mobs', 'mine_locations.id', '=', 'mobs.mine_location_id')
            ->where('mine_locations.is_active', true)
            ->where('mobs.mob_type', 'mine')
            ->where('mobs.hp', '>', 0)
            ->select('mine_locations.*')
            ->first();
        
        if (!$mineLocationData) {
            $this->error("❌ Не найдена локация рудника с живыми мобами!");
            return;
        }
        
        $mineLocation = MineLocation::find($mineLocationData->id);
        $this->info("📍 Локация рудника: {$mineLocation->name} (ID: {$mineLocation->id})");
        
        // 4. Проверяем мобов в локации
        $mobs = Mob::where('mine_location_id', $mineLocation->id)
            ->where('mob_type', 'mine')
            ->where('hp', '>', 0)
            ->get();
        
        $this->info("🐉 Мобов в локации: {$mobs->count()}");
        foreach ($mobs as $mob) {
            $this->info("   - {$mob->name} (ID: {$mob->id}, HP: {$mob->hp}/{$mob->max_hp})");
        }
        
        // 5. Устанавливаем локацию игрока
        if (!$player->statistics) {
            $this->error("❌ У игрока нет статистики!");
            return;
        }
        
        $player->statistics->current_location = $mineLocation->name;
        $player->statistics->save();
        $this->info("✅ Локация игрока установлена: {$mineLocation->name}");
        
        // 6. Удаляем старые метки и создаем новую
        MineMark::where('player_id', $player->id)->delete();
        $this->info("🗑️  Удалены старые метки");
        
        $mineDetectionService = app(MineDetectionService::class);
        $mark = $mineDetectionService->createMark($player, $mineLocation, 600); // 10 минут
        
        $this->info("✅ Создана метка 'Замечен':");
        $this->info("   ID: {$mark->id}");
        $this->info("   Mine Location: {$mark->mine_location_id}");
        $this->info("   Истекает: {$mark->expires_at}");
        
        // 7. Проверяем, что метка активна
        $activeMark = MineMark::where('player_id', $player->id)
            ->where('mine_location_id', $mineLocation->id)
            ->where('is_active', true)
            ->where('expires_at', '>', now())
            ->first();
        
        if ($activeMark) {
            $this->info("✅ Метка активна и найдена в БД");
        } else {
            $this->error("❌ Метка не найдена или неактивна!");
            return;
        }
        
        // 8. Запускаем автоатаку вручную
        $this->info("\n🎯 ЗАПУСК АВТОАТАКИ...");
        $this->info("HP до атаки: {$currentHP}");
        
        try {
            // Запускаем Job синхронно
            $job = new MineAutoAttackJob();
            $job->handle(
                app(\App\Services\MineDetectionService::class),
                app(\App\Services\MineTargetDistributionService::class),
                app(\App\Services\BattleLogService::class),
                app(\App\Services\PlayerHealthService::class),
                app(\App\Services\CombatFormulaService::class),
                app(\App\Services\LogFormattingService::class)
            );
            
            // Проверяем HP после атаки
            $newHP = $playerHealthService->getCurrentHP($player);
            $this->info("HP после атаки: {$newHP}");
            
            if ($newHP < $currentHP) {
                $damage = $currentHP - $newHP;
                $this->info("✅ АВТОАТАКА СРАБОТАЛА! Урон: {$damage}");
            } else {
                $this->warn("⚠️  Автоатака не нанесла урон");
            }
            
        } catch (\Exception $e) {
            $this->error("❌ Ошибка при выполнении автоатаки: " . $e->getMessage());
            $this->error("Стек: " . $e->getTraceAsString());
        }
        
        // 9. Проверяем обновление метки
        $updatedMark = MineMark::find($mark->id);
        if ($updatedMark && $updatedMark->last_attack_at) {
            $this->info("✅ Метка обновлена: последняя атака в {$updatedMark->last_attack_at}");
            $this->info("   Количество атак: {$updatedMark->attack_count}");
        } else {
            $this->warn("⚠️  Метка не была обновлена после атаки");
        }
        
        $this->newLine();
        $this->info("=== ТЕСТ ЗАВЕРШЕН ===");
    }
}
