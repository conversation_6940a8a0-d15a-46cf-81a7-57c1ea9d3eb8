<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Mob;
use App\Models\MineMark;
use App\Models\MineLocation;
use App\Services\MineDetectionService;
use App\Services\PlayerHealthService;
use Illuminate\Support\Facades\DB;

class FixAdminMineAttackIssue extends Command
{
    protected $signature = 'mine:fix-admin-attack-issue';
    protected $description = 'Исправляет конкретную проблему с автоатаками для игрока admin';

    public function handle()
    {
        $this->info("=== ИСПРАВЛЕНИЕ ПРОБЛЕМЫ АВТОАТАК ДЛЯ ADMIN ===");
        $this->newLine();

        // 1. Находим игрока admin
        $admin = User::where('name', 'admin')->first();
        if (!$admin) {
            $this->error("❌ Игрок 'admin' не найден!");
            return;
        }

        $this->info("👤 Игрок найден: {$admin->name} (ID: {$admin->id})");

        // 2. Находим моба "Огр" в локации "аааааааааааа"
        $targetLocation = MineLocation::where('name', 'аааааааааааа')->first();
        if (!$targetLocation) {
            $this->error("❌ Локация 'аааааааааааа' не найдена!");
            return;
        }

        $this->info("📍 Целевая локация: {$targetLocation->name} (ID: {$targetLocation->id})");

        // 3. Проверяем мобов в этой локации
        $mobsInLocation = Mob::where('mine_location_id', $targetLocation->id)
            ->where('mob_type', 'mine')
            ->where('hp', '>', 0)
            ->get();

        $this->info("🐉 Мобов в локации '{$targetLocation->name}': {$mobsInLocation->count()}");

        if ($mobsInLocation->isEmpty()) {
            // Переносим моба "Огр" в эту локацию
            $ogre = Mob::where('name', 'like', '%Огр%')
                ->where('mob_type', 'mine')
                ->where('hp', '>', 0)
                ->first();

            if ($ogre) {
                $oldLocation = $ogre->mine_location_id;
                $ogre->mine_location_id = $targetLocation->id;
                $ogre->save();

                $this->info("✅ Моб '{$ogre->name}' перенесен из локации {$oldLocation} в {$targetLocation->id}");
            } else {
                $this->error("❌ Не найден живой моб 'Огр' для переноса!");
                return;
            }
        } else {
            foreach ($mobsInLocation as $mob) {
                $this->info("   - {$mob->name} (ID: {$mob->id}, HP: {$mob->hp}/{$mob->max_hp})");
            }
        }

        // 4. Проверяем HP игрока
        $playerHealthService = app(PlayerHealthService::class);
        $currentHP = $playerHealthService->getCurrentHP($admin);
        $maxHP = $admin->getMaxHP();
        
        $this->info("💚 HP игрока: {$currentHP}/{$maxHP}");

        // 5. Устанавливаем правильную локацию игрока
        if (!$admin->statistics) {
            $this->error("❌ У игрока нет статистики!");
            return;
        }

        $admin->statistics->current_location = $targetLocation->name;
        $admin->statistics->save();
        $this->info("✅ Локация игрока установлена: {$targetLocation->name}");

        // 6. Удаляем старые метки и создаем новую в правильной локации
        MineMark::where('player_id', $admin->id)->delete();
        $this->info("🗑️  Удалены старые метки");

        $mineDetectionService = app(MineDetectionService::class);
        $mark = $mineDetectionService->createMark($admin, $targetLocation, 600); // 10 минут

        $this->info("✅ Создана метка 'Замечен' в правильной локации:");
        $this->info("   ID: {$mark->id}");
        $this->info("   Mine Location: {$mark->mine_location_id}");
        $this->info("   Location Name: {$mark->location_name}");
        $this->info("   Истекает: {$mark->expires_at}");

        // 7. Проверяем соответствие данных
        $this->info("\n🔍 ПРОВЕРКА СООТВЕТСТВИЯ:");
        $this->info("   Игрок в локации: {$admin->statistics->current_location}");
        $this->info("   Метка в локации: {$mark->location_name}");
        $this->info("   Мобы в mine_location_id: {$targetLocation->id}");

        // 8. Тестируем автоатаку
        $this->info("\n🎯 ТЕСТИРОВАНИЕ АВТОАТАКИ...");
        $this->info("HP до атаки: {$currentHP}");

        try {
            // Запускаем Job синхронно
            $job = new \App\Jobs\MineAutoAttackJob();
            $job->handle(
                app(\App\Services\MineDetectionService::class),
                app(\App\Services\MineTargetDistributionService::class),
                app(\App\Services\BattleLogService::class),
                app(\App\Services\PlayerHealthService::class),
                app(\App\Services\CombatFormulaService::class),
                app(\App\Services\LogFormattingService::class)
            );

            // Проверяем HP после атаки
            $newHP = $playerHealthService->getCurrentHP($admin);
            $this->info("HP после атаки: {$newHP}");

            if ($newHP < $currentHP) {
                $damage = $currentHP - $newHP;
                $this->info("✅ ПРОБЛЕМА РЕШЕНА! Автоатака работает! Урон: {$damage}");
            } else {
                $this->warn("⚠️  Автоатака все еще не работает");
                
                // Дополнительная диагностика
                $this->info("\n🔍 ДОПОЛНИТЕЛЬНАЯ ДИАГНОСТИКА:");
                
                // Проверяем активные метки
                $activeMarks = MineMark::where('player_id', $admin->id)
                    ->where('is_active', true)
                    ->where('expires_at', '>', now())
                    ->get();
                
                $this->info("   Активных меток: {$activeMarks->count()}");
                
                // Проверяем мобов
                $availableMobs = Mob::where('mine_location_id', $targetLocation->id)
                    ->where('mob_type', 'mine')
                    ->where('hp', '>', 0)
                    ->get();
                
                $this->info("   Доступных мобов: {$availableMobs->count()}");
            }

        } catch (\Exception $e) {
            $this->error("❌ Ошибка при выполнении автоатаки: " . $e->getMessage());
        }

        $this->newLine();
        $this->info("=== ИСПРАВЛЕНИЕ ЗАВЕРШЕНО ===");
    }
}
