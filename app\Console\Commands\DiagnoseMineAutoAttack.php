<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Mob;
use App\Models\MineMark;
use App\Models\MineLocation;
use App\Services\MineDetectionService;
use App\Services\MineTargetDistributionService;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;

class DiagnoseMineAutoAttack extends Command
{
    protected $signature = 'mine:diagnose-auto-attack {player_name=admin}';
    protected $description = 'Диагностика системы автоатак мобов в рудниках для конкретного игрока';

    public function handle()
    {
        $playerName = $this->argument('player_name');
        
        $this->info("=== ДИАГНОСТИКА СИСТЕМЫ АВТОАТАК МОБОВ В РУДНИКАХ ===");
        $this->info("Игрок: {$playerName}");
        $this->newLine();

        // 1. Проверяем игрока
        $this->checkPlayer($playerName);
        
        // 2. Проверяем мобов "Огр"
        $this->checkOgreMobs();
        
        // 3. Проверяем метки MineMark
        $this->checkMineMarks($playerName);
        
        // 4. Проверяем локации рудников
        $this->checkMineLocations();
        
        // 5. Проверяем Redis назначения
        $this->checkRedisAssignments();
        
        // 6. Тестируем компоненты системы
        $this->testSystemComponents($playerName);
        
        $this->newLine();
        $this->info("=== ДИАГНОСТИКА ЗАВЕРШЕНА ===");
    }

    private function checkPlayer($playerName)
    {
        $this->info("1. ПРОВЕРКА ИГРОКА");
        $this->line("─────────────────────");
        
        $player = User::where('name', $playerName)->first();
        
        if (!$player) {
            $this->error("❌ Игрок '{$playerName}' не найден!");
            return;
        }
        
        $this->info("✅ Игрок найден:");
        $this->line("   ID: {$player->id}");
        $this->line("   HP: {$player->hp}/{$player->max_hp}");
        $this->line("   Локация ID: " . ($player->location_id ?? 'не установлена'));
        
        if ($player->location) {
            $this->line("   Название локации: {$player->location->name}");
        }
        
        $this->newLine();
    }

    private function checkOgreMobs()
    {
        $this->info("2. ПРОВЕРКА МОБОВ 'ОГР'");
        $this->line("─────────────────────");
        
        $ogres = Mob::where('name', 'like', '%Огр%')->get();
        
        if ($ogres->isEmpty()) {
            $this->error("❌ Мобы 'Огр' не найдены!");
            return;
        }
        
        $this->info("✅ Найдено мобов 'Огр': {$ogres->count()}");
        
        foreach ($ogres as $ogre) {
            $this->line("   ID: {$ogre->id}");
            $this->line("   Название: {$ogre->name}");
            $this->line("   HP: {$ogre->hp}/{$ogre->max_hp}");
            $this->line("   Тип: " . ($ogre->mob_type ?? 'не установлен'));
            $this->line("   Локация ID: " . ($ogre->location_id ?? 'не установлена'));
            $this->line("   Mine Location ID: " . ($ogre->mine_location_id ?? 'не установлена'));
            
            if ($ogre->mob_type !== 'mine') {
                $this->warn("   ⚠️  ПРОБЛЕМА: mob_type должен быть 'mine', а не '{$ogre->mob_type}'");
            }
            
            $this->line("");
        }
        
        $this->newLine();
    }

    private function checkMineMarks($playerName)
    {
        $this->info("3. ПРОВЕРКА МЕТОК MINE_MARKS");
        $this->line("─────────────────────────────");
        
        $player = User::where('name', $playerName)->first();
        if (!$player) {
            $this->error("❌ Игрок не найден для проверки меток");
            return;
        }
        
        try {
            $marks = MineMark::where('player_id', $player->id)
                ->where('is_active', true)
                ->where('expires_at', '>', now())
                ->get();
            
            if ($marks->isEmpty()) {
                $this->warn("⚠️  Активных меток для игрока '{$playerName}' не найдено");
            } else {
                $this->info("✅ Найдено активных меток: {$marks->count()}");
                
                foreach ($marks as $mark) {
                    $this->line("   ID: {$mark->id}");
                    $this->line("   Mine Location ID: {$mark->mine_location_id}");
                    $this->line("   Location ID: {$mark->location_id}");
                    $this->line("   Location Name: {$mark->location_name}");
                    $this->line("   Истекает: {$mark->expires_at}");
                    $this->line("   Последняя атака: " . ($mark->last_attack_at ?? 'никогда'));
                    $this->line("   Количество атак: {$mark->attack_count}");
                    $this->line("");
                }
            }
        } catch (\Exception $e) {
            $this->error("❌ Ошибка при проверке меток: " . $e->getMessage());
        }
        
        $this->newLine();
    }

    private function checkMineLocations()
    {
        $this->info("4. ПРОВЕРКА ЛОКАЦИЙ РУДНИКОВ");
        $this->line("─────────────────────────────");
        
        $mineLocations = MineLocation::where('is_active', true)->get();
        
        if ($mineLocations->isEmpty()) {
            $this->error("❌ Активных локаций рудников не найдено!");
            return;
        }
        
        $this->info("✅ Найдено активных локаций: {$mineLocations->count()}");
        
        foreach ($mineLocations as $location) {
            $this->line("   ID: {$location->id}");
            $this->line("   Название: {$location->name}");
            $this->line("   Slug: {$location->slug}");
            
            // Проверяем мобов в этой локации
            $mobsCount = Mob::where('mine_location_id', $location->id)
                ->where('mob_type', 'mine')
                ->where('hp', '>', 0)
                ->count();
            
            $this->line("   Мобов в локации: {$mobsCount}");
            $this->line("");
        }
        
        $this->newLine();
    }

    private function checkRedisAssignments()
    {
        $this->info("5. ПРОВЕРКА REDIS НАЗНАЧЕНИЙ");
        $this->line("─────────────────────────────");
        
        try {
            $mineLocations = MineLocation::where('is_active', true)->get();
            
            foreach ($mineLocations as $location) {
                $redisKey = "mine_mob_targets:{$location->id}";
                $assignments = Redis::hgetall($redisKey);
                
                $this->line("   Локация: {$location->name} (ID: {$location->id})");
                $this->line("   Redis ключ: {$redisKey}");
                $this->line("   Назначений: " . count($assignments));
                
                if (!empty($assignments)) {
                    foreach ($assignments as $playerId => $mobIds) {
                        $mobIdsArray = json_decode($mobIds, true) ?? [];
                        $this->line("     Игрок {$playerId}: мобы " . implode(', ', $mobIdsArray));
                    }
                }
                
                $this->line("");
            }
        } catch (\Exception $e) {
            $this->error("❌ Ошибка при проверке Redis: " . $e->getMessage());
        }
        
        $this->newLine();
    }

    private function testSystemComponents($playerName)
    {
        $this->info("6. ТЕСТИРОВАНИЕ КОМПОНЕНТОВ СИСТЕМЫ");
        $this->line("───────────────────────────────────");
        
        $player = User::where('name', $playerName)->first();
        if (!$player) {
            $this->error("❌ Игрок не найден для тестирования");
            return;
        }
        
        try {
            // Тестируем MineDetectionService
            $mineDetectionService = app(MineDetectionService::class);
            $markedPlayers = $mineDetectionService->getActiveMarkedPlayers();
            
            $this->line("✅ MineDetectionService работает");
            $this->line("   Замеченных игроков: " . count($markedPlayers));
            
            $playerFound = false;
            foreach ($markedPlayers as $markedPlayer) {
                if ($markedPlayer['player']->id === $player->id) {
                    $playerFound = true;
                    $this->line("   ✅ Игрок '{$playerName}' найден среди замеченных");
                    break;
                }
            }
            
            if (!$playerFound) {
                $this->warn("   ⚠️  Игрок '{$playerName}' НЕ найден среди замеченных");
            }
            
        } catch (\Exception $e) {
            $this->error("❌ Ошибка при тестировании MineDetectionService: " . $e->getMessage());
        }
        
        $this->newLine();
    }
}
