<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Mob;
use App\Models\MineMark;
use App\Models\MineLocation;
use App\Services\MineDetectionService;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;

class FixMineAutoAttackIssues extends Command
{
    protected $signature = 'mine:fix-auto-attack-issues {player_name=admin}';
    protected $description = 'Исправление проблем с автоатаками мобов в рудниках для конкретного игрока';

    public function handle()
    {
        $playerName = $this->argument('player_name');
        
        $this->info("=== ИСПРАВЛЕНИЕ ПРОБЛЕМ АВТОАТАК МОБОВ В РУДНИКАХ ===");
        $this->info("Игрок: {$playerName}");
        $this->newLine();

        // 1. Исправляем данные игрока
        $player = $this->fixPlayerData($playerName);
        if (!$player) {
            $this->error("❌ Не удалось найти или исправить данные игрока");
            return;
        }
        
        // 2. Исправляем мобов
        $this->fixMobData();
        
        // 3. Создаем метку для игрока
        $this->createTestMark($player);
        
        // 4. Очищаем Redis назначения
        $this->cleanupRedisAssignments();
        
        // 5. Тестируем систему
        $this->testAutoAttackSystem($player);
        
        $this->newLine();
        $this->info("=== ИСПРАВЛЕНИЕ ЗАВЕРШЕНО ===");
    }

    private function fixPlayerData($playerName)
    {
        $this->info("1. ИСПРАВЛЕНИЕ ДАННЫХ ИГРОКА");
        $this->line("─────────────────────────────");
        
        $player = User::where('name', $playerName)->first();
        
        if (!$player) {
            $this->error("❌ Игрок '{$playerName}' не найден!");
            return null;
        }
        
        $updated = false;
        
        // Исправляем HP если пустое
        if (empty($player->hp) || $player->hp <= 0) {
            $player->hp = $player->max_hp ?: 100;
            $updated = true;
            $this->info("   ✅ Исправлено HP: {$player->hp}");
        }
        
        // Устанавливаем локацию если не установлена
        if (empty($player->location_id)) {
            // Найдем активную локацию рудника
            $mineLocation = MineLocation::where('is_active', true)
                ->whereNotNull('location_id')
                ->first();
            
            if ($mineLocation) {
                $player->location_id = $mineLocation->location_id;
                $updated = true;
                $this->info("   ✅ Установлена локация: {$player->location_id}");
            }
        }
        
        if ($updated) {
            $player->save();
            $this->info("   💾 Данные игрока сохранены");
        } else {
            $this->info("   ✅ Данные игрока в порядке");
        }
        
        $this->newLine();
        return $player;
    }

    private function fixMobData()
    {
        $this->info("2. ИСПРАВЛЕНИЕ ДАННЫХ МОБОВ");
        $this->line("─────────────────────────────");
        
        // Исправляем мобов "Огр" без mine_location_id
        $ogres = Mob::where('name', 'like', '%Огр%')
            ->where('mob_type', 'mine')
            ->whereNull('mine_location_id')
            ->get();
        
        if ($ogres->isNotEmpty()) {
            $this->info("   🔧 Найдено мобов 'Огр' без mine_location_id: {$ogres->count()}");
            
            foreach ($ogres as $ogre) {
                // Найдем подходящую локацию рудника
                $mineLocation = MineLocation::where('is_active', true)
                    ->where('location_id', $ogre->location_id)
                    ->first();
                
                if ($mineLocation) {
                    $ogre->mine_location_id = $mineLocation->id;
                    $ogre->save();
                    
                    $this->info("   ✅ Моб '{$ogre->name}' (ID: {$ogre->id}) привязан к mine_location_id: {$mineLocation->id}");
                } else {
                    $this->warn("   ⚠️  Не найдена подходящая mine_location для моба ID: {$ogre->id}");
                }
            }
        } else {
            $this->info("   ✅ Все мобы 'Огр' имеют правильные mine_location_id");
        }
        
        $this->newLine();
    }

    private function createTestMark($player)
    {
        $this->info("3. СОЗДАНИЕ ТЕСТОВОЙ МЕТКИ");
        $this->line("─────────────────────────────");
        
        // Найдем локацию рудника с мобами
        $mineLocation = MineLocation::where('is_active', true)
            ->whereHas('mobs', function($query) {
                $query->where('mob_type', 'mine')
                      ->where('hp', '>', 0);
            })
            ->first();
        
        if (!$mineLocation) {
            $this->error("   ❌ Не найдена активная локация рудника с мобами");
            return;
        }
        
        $this->info("   📍 Используем локацию: {$mineLocation->name} (ID: {$mineLocation->id})");
        
        // Удаляем существующие метки игрока
        MineMark::where('player_id', $player->id)->delete();
        $this->info("   🗑️  Удалены старые метки игрока");
        
        // Создаем новую метку
        try {
            $mineDetectionService = app(MineDetectionService::class);
            $mark = $mineDetectionService->createMark($player, $mineLocation, 600); // 10 минут
            
            $this->info("   ✅ Создана метка 'Замечен':");
            $this->info("      ID: {$mark->id}");
            $this->info("      Mine Location: {$mark->mine_location_id}");
            $this->info("      Location: {$mark->location_id}");
            $this->info("      Истекает: {$mark->expires_at}");
            
        } catch (\Exception $e) {
            $this->error("   ❌ Ошибка при создании метки: " . $e->getMessage());
        }
        
        $this->newLine();
    }

    private function cleanupRedisAssignments()
    {
        $this->info("4. ОЧИСТКА REDIS НАЗНАЧЕНИЙ");
        $this->line("─────────────────────────────");
        
        try {
            $mineLocations = MineLocation::where('is_active', true)->get();
            $cleanedCount = 0;
            
            foreach ($mineLocations as $location) {
                $redisKey = "mine_mob_targets:{$location->id}";
                
                if (Redis::exists($redisKey)) {
                    Redis::del($redisKey);
                    $cleanedCount++;
                }
            }
            
            $this->info("   ✅ Очищено Redis ключей: {$cleanedCount}");
            
        } catch (\Exception $e) {
            $this->error("   ❌ Ошибка при очистке Redis: " . $e->getMessage());
        }
        
        $this->newLine();
    }

    private function testAutoAttackSystem($player)
    {
        $this->info("5. ТЕСТИРОВАНИЕ СИСТЕМЫ АВТОАТАК");
        $this->line("─────────────────────────────────");
        
        try {
            // Проверяем наличие активных меток
            $mineDetectionService = app(MineDetectionService::class);
            
            // Получаем все активные метки
            $allMarks = MineMark::where('is_active', true)
                ->where('expires_at', '>', now())
                ->get();
            
            $this->info("   📊 Всего активных меток: {$allMarks->count()}");
            
            // Проверяем метки конкретного игрока
            $playerMarks = $allMarks->where('player_id', $player->id);
            $this->info("   👤 Меток игрока '{$player->name}': {$playerMarks->count()}");
            
            if ($playerMarks->isNotEmpty()) {
                foreach ($playerMarks as $mark) {
                    $this->info("      ✅ Метка ID: {$mark->id}, Mine Location: {$mark->mine_location_id}");
                    
                    // Проверяем мобов в этой локации
                    $mobsCount = Mob::where('mine_location_id', $mark->mine_location_id)
                        ->where('mob_type', 'mine')
                        ->where('hp', '>', 0)
                        ->count();
                    
                    $this->info("      🐉 Мобов в локации: {$mobsCount}");
                }
            }
            
            // Запускаем тестовую автоатаку
            $this->info("\n   🎯 Запуск тестовой автоатаки...");
            
            $initialHp = $player->fresh()->hp;
            $this->info("      HP до атаки: {$initialHp}");
            
            // Запускаем Job автоатаки
            \App\Jobs\MineAutoAttackJob::dispatch();
            
            // Ждем немного и проверяем результат
            sleep(2);
            
            $finalHp = $player->fresh()->hp;
            $this->info("      HP после атаки: {$finalHp}");
            
            if ($finalHp < $initialHp) {
                $damage = $initialHp - $finalHp;
                $this->info("      ✅ Автоатака сработала! Урон: {$damage}");
            } else {
                $this->warn("      ⚠️  Автоатака не сработала или урон не нанесен");
            }
            
        } catch (\Exception $e) {
            $this->error("   ❌ Ошибка при тестировании: " . $e->getMessage());
        }
        
        $this->newLine();
    }
}
